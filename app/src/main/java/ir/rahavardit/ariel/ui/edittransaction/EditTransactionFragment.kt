package ir.rahavardit.ariel.ui.edittransaction

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.data.model.BankItem
import ir.rahavardit.ariel.data.model.ExpenditureObject
import ir.rahavardit.ariel.data.model.IncomeObject
import ir.rahavardit.ariel.data.model.TagItem
import ir.rahavardit.ariel.data.model.TransactionCategoryItem
import ir.rahavardit.ariel.data.model.TransactionMode
import ir.rahavardit.ariel.databinding.FragmentEditTransactionBinding
import ir.rahavardit.ariel.ui.components.JalaliDatePickerDialog
import ir.rahavardit.ariel.utils.JalaliDateUtils
import ir.rahavardit.ariel.utils.PersianUtils

/**
 * Fragment for editing an existing transaction.
 */
class EditTransactionFragment : Fragment() {

    private var _binding: FragmentEditTransactionBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: EditTransactionViewModel
    private lateinit var sessionManager: SessionManager
    private var shortUuid: String? = null
    private var mode: String? = null
    private var transaction: Any? = null

    private var selectedDate: String? = null
    private var selectedMode: String? = null
    private var selectedBankId: Int? = null
    private var selectedCategoryId: Int? = null
    private val selectedTagIds = mutableListOf<Int>()
    private val selectedTagTitles = mutableListOf<String>()

    private var availableTags: List<TagItem> = emptyList()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(EditTransactionViewModel::class.java)
        _binding = FragmentEditTransactionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        shortUuid = arguments?.getString("shortUuid")
        mode = arguments?.getString("mode")
        transaction = arguments?.getParcelable("transaction")

        // Check if we have the transaction data
        if (transaction == null) {
            Toast.makeText(
                requireContext(),
                getString(R.string.transaction_data_not_found),
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        // Populate the form with the transaction data
        populateForm()
        setupListeners()
        observeViewModel()
        fetchData()
    }

    /**
     * Populates the form with the transaction data.
     */
    private fun populateForm() {
        when (transaction) {
            is IncomeObject -> {
                val income = transaction as IncomeObject
                binding.etTransactionTitle.setText(income.title)
                binding.etTransactionAmount.setText(income.amount.toString())
                selectedDate = "${income.year}/${String.format("%02d", income.month)}/${String.format("%02d", income.day)}"
                binding.etTransactionDate.setText(PersianUtils.convertToPersianNumerals(income.slashedDatePersian))
                selectedMode = income.mode
                selectedBankId = income.bankInfo.id
                selectedCategoryId = income.categoryInfo.id
                selectedTagIds.clear()
                selectedTagIds.addAll(income.tags)
                selectedTagTitles.clear()
                selectedTagTitles.addAll(income.tagsNames)
                updateTagsDisplay()
            }
            is ExpenditureObject -> {
                val expenditure = transaction as ExpenditureObject
                binding.etTransactionTitle.setText(expenditure.title)
                binding.etTransactionAmount.setText(expenditure.amount.toString())
                selectedDate = "${expenditure.year}/${String.format("%02d", expenditure.month)}/${String.format("%02d", expenditure.day)}"
                binding.etTransactionDate.setText(PersianUtils.convertToPersianNumerals(expenditure.slashedDatePersian))
                selectedMode = expenditure.mode
                selectedBankId = expenditure.bankInfo.id
                selectedCategoryId = expenditure.categoryInfo.id
                selectedTagIds.clear()
                selectedTagIds.addAll(expenditure.tags)
                selectedTagTitles.clear()
                selectedTagTitles.addAll(expenditure.tagsNames)
                updateTagsDisplay()
            }
        }
    }

    /**
     * Sets up click listeners and other UI interactions.
     */
    private fun setupListeners() {
        // Date picker
        binding.etTransactionDate.setOnClickListener {
            showDatePicker()
        }

        // Tags multi-select
        binding.etTransactionTags.setOnClickListener {
            showTagsDialog()
        }

        // Mode dropdown
        binding.dropdownTransactionMode.setOnItemClickListener { _, _, position, _ ->
            val modes = viewModel.transactionModes.value
            if (modes != null && position < modes.size) {
                selectedMode = modes[position].value
                binding.tilTransactionMode.error = null
            }
        }

        // Bank dropdown
        binding.dropdownTransactionBank.setOnItemClickListener { _, _, position, _ ->
            val banks = viewModel.banks.value
            if (banks != null && position < banks.size) {
                selectedBankId = banks[position].id
                binding.tilTransactionBank.error = null
            }
        }

        // Category dropdown
        binding.dropdownTransactionCategory.setOnItemClickListener { _, _, position, _ ->
            val categories = viewModel.categories.value
            if (categories != null && position < categories.size) {
                selectedCategoryId = categories[position].id
                binding.tilTransactionCategory.error = null
            }
        }

        // Submit button
        binding.btnSubmit.setOnClickListener {
            val title = binding.etTransactionTitle.text.toString().trim()
            val amount = binding.etTransactionAmount.text.toString().trim()

            val validationResult = viewModel.validateInputs(
                selectedDate,
                amount,
                selectedMode,
                selectedCategoryId?.toString()
            )

            when {
                !validationResult.isDateValid -> {
                    binding.tilTransactionDate.error = getString(R.string.please_select_date)
                }
                !validationResult.isAmountValid -> {
                    binding.tilTransactionAmount.error = getString(R.string.please_enter_valid_amount)
                }
                !validationResult.isModeValid -> {
                    binding.tilTransactionMode.error = getString(R.string.please_select_mode)
                }
                !validationResult.isCategoryValid -> {
                    binding.tilTransactionCategory.error = getString(R.string.please_select_category)
                }
                else -> {
                    // Clear any previous errors
                    binding.tilTransactionDate.error = null
                    binding.tilTransactionAmount.error = null
                    binding.tilTransactionMode.error = null
                    binding.tilTransactionCategory.error = null

                    // Update transaction
                    val token = sessionManager.getAuthToken()
                    if (token != null && shortUuid != null) {
                        viewModel.updateTransaction(
                            token = token,
                            shortUuid = shortUuid!!,
                            date = selectedDate!!,
                            title = title.ifBlank { null },
                            amount = amount.toInt(),
                            mode = selectedMode!!,
                            bankId = selectedBankId,
                            categoryId = selectedCategoryId!!,
                            tagIds = selectedTagIds.toList()
                        )
                    } else if (token == null) {
                        showError(getString(R.string.authentication_token_not_found))
                    } else {
                        showError(getString(R.string.transaction_id_not_found))
                    }
                }
            }
        }
    }

    private fun fetchData() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.loadBanks(token)
            viewModel.loadCategories(token)
            viewModel.loadTags(token)
        }
    }

    private fun showDatePicker() {
        val currentDate = selectedDate ?: JalaliDateUtils.getCurrentJalaliDate()
        val dateParts = currentDate.split("/")
        val year = dateParts[0].toInt()
        val month = dateParts[1].toInt()
        val day = dateParts[2].toInt()

        val datePickerDialog = JalaliDatePickerDialog(requireContext(), year, month, day) { selectedYear, selectedMonth, selectedDay ->
            selectedDate = "$selectedYear/${String.format("%02d", selectedMonth)}/${String.format("%02d", selectedDay)}"
            val persianDate = PersianUtils.convertToPersianNumerals("$selectedYear/$selectedMonth/$selectedDay")
            binding.etTransactionDate.setText(persianDate)
            binding.tilTransactionDate.error = null
        }
        datePickerDialog.show()
    }

    private fun showTagsDialog() {
        if (availableTags.isEmpty()) {
            Toast.makeText(requireContext(), getString(R.string.no_tags_available), Toast.LENGTH_SHORT).show()
            return
        }

        val tagTitles = availableTags.map { it.title }.toTypedArray()
        val checkedItems = BooleanArray(tagTitles.size) { index ->
            selectedTagIds.contains(availableTags[index].id)
        }

        MaterialAlertDialogBuilder(requireContext())
            .setTitle(getString(R.string.select_tags))
            .setMultiChoiceItems(tagTitles, checkedItems) { _, which, isChecked ->
                val tag = availableTags[which]
                if (isChecked) {
                    if (!selectedTagIds.contains(tag.id)) {
                        selectedTagIds.add(tag.id)
                        selectedTagTitles.add(tag.title)
                    }
                } else {
                    selectedTagIds.remove(tag.id)
                    selectedTagTitles.remove(tag.title)
                }
            }
            .setPositiveButton(getString(R.string.ok)) { _, _ ->
                updateTagsDisplay()
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }

    private fun updateTagsDisplay() {
        binding.etTransactionTags.setText(selectedTagTitles.joinToString(", "))
    }

    /**
     * Observes ViewModel LiveData.
     */
    private fun observeViewModel() {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        viewModel.transactionModes.observe(viewLifecycleOwner) { modes ->
            setupModeDropdown(modes)
        }

        viewModel.banks.observe(viewLifecycleOwner) { banks ->
            setupBankDropdown(banks)
        }

        viewModel.categories.observe(viewLifecycleOwner) { categories ->
            setupCategoryDropdown(categories)
        }

        viewModel.tags.observe(viewLifecycleOwner) { tags ->
            availableTags = tags
        }

        viewModel.transactionUpdateResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is EditTransactionViewModel.TransactionUpdateResult.Success -> {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.transaction_updated_successfully),
                        Toast.LENGTH_SHORT
                    ).show()

                    // Navigate back to homepage
                    findNavController().navigate(R.id.nav_home)
                }
                is EditTransactionViewModel.TransactionUpdateResult.Error -> {
                    showError(result.errorMessage)
                }
            }
        }
    }

    private fun setupModeDropdown(modes: List<TransactionMode>) {
        val modeLabels = modes.map { it.label }
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, modeLabels)
        binding.dropdownTransactionMode.setAdapter(adapter)

        // Set selected mode
        val selectedIndex = modes.indexOfFirst { it.value == selectedMode }
        if (selectedIndex >= 0) {
            binding.dropdownTransactionMode.setText(modes[selectedIndex].label, false)
        }
    }

    private fun setupBankDropdown(banks: List<BankItem>) {
        val bankTitles = banks.map { it.title }
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, bankTitles)
        binding.dropdownTransactionBank.setAdapter(adapter)

        // Set selected bank
        val selectedIndex = banks.indexOfFirst { it.id == selectedBankId }
        if (selectedIndex >= 0) {
            binding.dropdownTransactionBank.setText(banks[selectedIndex].title, false)
        }
    }

    private fun setupCategoryDropdown(categories: List<TransactionCategoryItem>) {
        val categoryTitles = categories.map { it.title }
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, categoryTitles)
        binding.dropdownTransactionCategory.setAdapter(adapter)

        // Set selected category
        val selectedIndex = categories.indexOfFirst { it.id == selectedCategoryId }
        if (selectedIndex >= 0) {
            binding.dropdownTransactionCategory.setText(categories[selectedIndex].title, false)
        }
    }

    /**
     * Shows an error message.
     *
     * @param message The error message to show.
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
