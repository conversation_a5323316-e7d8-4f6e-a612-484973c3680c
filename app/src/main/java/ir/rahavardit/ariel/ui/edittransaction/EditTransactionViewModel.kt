package ir.rahavardit.ariel.ui.edittransaction

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.model.BankItem
import ir.rahavardit.ariel.data.model.TagItem
import ir.rahavardit.ariel.data.model.TransactionCategoryItem
import ir.rahavardit.ariel.data.model.TransactionMode
import ir.rahavardit.ariel.data.model.TransactionResponse
import ir.rahavardit.ariel.data.repository.TransactionRepository
import kotlinx.coroutines.launch

class EditTransactionViewModel : ViewModel() {

    private val transactionRepository = TransactionRepository()

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _transactionModes = MutableLiveData<List<TransactionMode>>()
    val transactionModes: LiveData<List<TransactionMode>> = _transactionModes

    private val _banks = MutableLiveData<List<BankItem>>()
    val banks: LiveData<List<BankItem>> = _banks

    private val _categories = MutableLiveData<List<TransactionCategoryItem>>()
    val categories: LiveData<List<TransactionCategoryItem>> = _categories

    private val _tags = MutableLiveData<List<TagItem>>()
    val tags: LiveData<List<TagItem>> = _tags

    private val _transactionUpdateResult = MutableLiveData<TransactionUpdateResult>()
    val transactionUpdateResult: LiveData<TransactionUpdateResult> = _transactionUpdateResult

    sealed class TransactionUpdateResult {
        data class Success(val transaction: TransactionResponse) : TransactionUpdateResult()
        data class Error(val errorMessage: String) : TransactionUpdateResult()
    }

    data class ValidationResult(
        val isDateValid: Boolean,
        val isAmountValid: Boolean,
        val isModeValid: Boolean,
        val isCategoryValid: Boolean
    )

    init {
        loadTransactionModes()
    }

    private fun loadTransactionModes() {
        _transactionModes.value = listOf(
            TransactionMode("income", "درآمد"),
            TransactionMode("expenditure", "هزینه")
        )
    }

    fun loadBanks(token: String) {
        viewModelScope.launch {
            try {
                val result = transactionRepository.getBanks(token)
                result.fold(
                    onSuccess = { bankResponse ->
                        _banks.value = bankResponse.results
                    },
                    onFailure = { exception ->
                        // Handle error silently or log it
                    }
                )
            } catch (e: Exception) {
                // Handle error silently or log it
            }
        }
    }

    fun loadCategories(token: String) {
        viewModelScope.launch {
            try {
                val result = transactionRepository.getTransactionCategories(token)
                result.fold(
                    onSuccess = { categoryResponse ->
                        _categories.value = categoryResponse.results
                    },
                    onFailure = { exception ->
                        // Handle error silently or log it
                    }
                )
            } catch (e: Exception) {
                // Handle error silently or log it
            }
        }
    }

    fun loadTags(token: String) {
        viewModelScope.launch {
            try {
                val result = transactionRepository.getTags(token)
                result.fold(
                    onSuccess = { tagResponse ->
                        _tags.value = tagResponse.results
                    },
                    onFailure = { exception ->
                        // Handle error silently or log it
                    }
                )
            } catch (e: Exception) {
                // Handle error silently or log it
            }
        }
    }

    fun validateInputs(
        date: String?,
        amount: String,
        mode: String?,
        categoryId: String?
    ): ValidationResult {
        return ValidationResult(
            isDateValid = !date.isNullOrBlank(),
            isAmountValid = amount.isNotBlank() && amount.toIntOrNull() != null && amount.toInt() > 0,
            isModeValid = !mode.isNullOrBlank(),
            isCategoryValid = !categoryId.isNullOrBlank()
        )
    }

    fun updateTransaction(
        token: String,
        shortUuid: String,
        date: String,
        title: String?,
        amount: Int,
        mode: String,
        bankId: Int?,
        categoryId: Int,
        tagIds: List<Int>
    ) {
        _isLoading.value = true

        viewModelScope.launch {
            try {
                val result = transactionRepository.updateTransaction(
                    token, shortUuid, date, title, amount, mode, bankId, categoryId, tagIds
                )

                result.fold(
                    onSuccess = { transaction ->
                        _transactionUpdateResult.value = TransactionUpdateResult.Success(transaction)
                    },
                    onFailure = { exception ->
                        _transactionUpdateResult.value = TransactionUpdateResult.Error(
                            exception.message ?: "Failed to update transaction"
                        )
                    }
                )
            } catch (e: Exception) {
                _transactionUpdateResult.value = TransactionUpdateResult.Error(
                    e.message ?: "Unknown error occurred"
                )
            } finally {
                _isLoading.value = false
            }
        }
    }
}
