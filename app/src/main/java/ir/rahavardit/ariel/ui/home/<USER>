package ir.rahavardit.ariel.ui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.google.android.material.tabs.TabLayoutMediator
import ir.rahavardit.ariel.ArielApplication
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.databinding.FragmentHomeBinding
import ir.rahavardit.ariel.data.model.HomepageDataResponse

class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null
    private lateinit var homeViewModel: HomeViewModel
    private lateinit var pagerAdapter: HomePagerAdapter

    // Store current selected values to prevent duplicate API calls
    private var currentSelectedYear: Int? = null
    private var currentSelectedMonthStart: Int? = null
    private var currentSelectedMonthEnd: Int? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val sessionManager = (requireActivity().application as ArielApplication).sessionManager
        val factory = HomeViewModelFactory(sessionManager)
        homeViewModel = ViewModelProvider(this, factory)[HomeViewModel::class.java]

        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupViewPager()
        setupObservers()

        return root
    }

    override fun onResume() {
        super.onResume()
        // refresh homepage data every time user navigates back to homepage
        homeViewModel.refreshHomepageData()
    }

    private fun setupViewPager() {
        // Setup ViewPager with tabs
        pagerAdapter = HomePagerAdapter(
            onDeleteTransaction = { shortUuid -> homeViewModel.deleteTransaction(shortUuid) },
            onDeleteEvent = { shortUuid -> homeViewModel.deleteEvent(shortUuid) },
            onEditIncome = { income -> navigateToEditTransaction(income.shortUuid, "income", income) },
            onEditExpenditure = { expenditure -> navigateToEditTransaction(expenditure.shortUuid, "expenditure", expenditure) },
            onEditEvent = { event -> navigateToEditEvent(event.shortUuid, event) }
        )
        binding.viewPager.adapter = pagerAdapter

        // Setup TabLayout with ViewPager
        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = when (position) {
                0 -> "درآمد"
                1 -> "هزینه"
                2 -> "رویداد"
                else -> ""
            }
        }.attach()
    }

    private fun setupObservers() {
        homeViewModel.homepageData.observe(viewLifecycleOwner) { data ->
            updateUI(data)
        }

        homeViewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        homeViewModel.errorMessage.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage.isNotEmpty()) {
                binding.errorMessage.text = errorMessage
                binding.errorMessage.visibility = View.VISIBLE
                Toast.makeText(requireContext(), errorMessage, Toast.LENGTH_SHORT).show()
            } else {
                binding.errorMessage.visibility = View.GONE
            }
        }

        homeViewModel.successMessage.observe(viewLifecycleOwner) { successMessage ->
            if (successMessage.isNotEmpty()) {
                Toast.makeText(requireContext(), successMessage, Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun updateUI(data: HomepageDataResponse) {
        // Update current selected values
        currentSelectedYear = data.chosenyear
        currentSelectedMonthStart = data.chosenmonthstart
        currentSelectedMonthEnd = data.chosenmonthend

        // Setup year spinner
        setupYearSpinner(data.years, data.chosenyear)

        // Setup month spinners
        setupMonthSpinners(data.namesOfMonthsPersian, data.chosenmonthstart, data.chosenmonthend)

        // Update ViewPager data
        pagerAdapter.updateData(
            data.incomeObjects,
            data.expenditureObjects,
            data.eventObjects
        )
    }

    private fun setupYearSpinner(years: List<Int>, selectedYear: Int) {
        // Convert years to Persian numerals
        val persianYears = years.map { convertToPersianNumerals(it.toString()) }

        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, persianYears)
        binding.yearSpinner.setAdapter(adapter)

        // Set selected year
        val selectedIndex = years.indexOf(selectedYear)
        if (selectedIndex >= 0) {
            binding.yearSpinner.setText(persianYears[selectedIndex], false)
        }

        // Set listener for year changes
        binding.yearSpinner.setOnItemClickListener { _, _, position, _ ->
            val selectedYearValue = years[position]
            // Only update if the selected year is different from the current one
            if (currentSelectedYear != selectedYearValue) {
                homeViewModel.updateFilters(year = selectedYearValue, monthStart = null, monthEnd = null)
            }
        }
    }

    private fun setupMonthSpinners(monthNames: List<String>, selectedMonthStart: Int, selectedMonthEnd: Int) {
        // Setup month start spinner
        val adapter1 = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, monthNames)
        binding.monthStartSpinner.setAdapter(adapter1)

        // Set selected month start (1-based index)
        if (selectedMonthStart > 0 && selectedMonthStart <= monthNames.size) {
            binding.monthStartSpinner.setText(monthNames[selectedMonthStart - 1], false)
        }

        // Setup month end spinner with empty option
        val monthNamesWithEmpty = mutableListOf<String>()
        monthNamesWithEmpty.add("") // Add empty option at the beginning
        monthNamesWithEmpty.addAll(monthNames)

        val adapter2 = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, monthNamesWithEmpty)
        binding.monthEndSpinner.setAdapter(adapter2)

        // Set selected month end (1-based index, accounting for empty option)
        if (selectedMonthEnd > 0 && selectedMonthEnd <= monthNames.size) {
            binding.monthEndSpinner.setText(monthNames[selectedMonthEnd - 1], false)
        } else {
            // Set to empty option if no month end is selected
            binding.monthEndSpinner.setText("", false)
        }

        // Set listeners for month changes
        binding.monthStartSpinner.setOnItemClickListener { _, _, position, _ ->
            val selectedMonthValue = position + 1 // Convert to 1-based
            // Only update if the selected month start is different from the current one
            if (currentSelectedMonthStart != selectedMonthValue) {
                homeViewModel.updateFilters(year = null, monthStart = selectedMonthValue, monthEnd = null)
            }
        }

        binding.monthEndSpinner.setOnItemClickListener { _, _, position, _ ->
            if (position == 0) {
                // Empty option selected - clear month end filter
                if (currentSelectedMonthEnd != null) {
                    homeViewModel.clearMonthEndFilter()
                }
            } else {
                val selectedMonthValue = position // position is already correct since we added empty option at index 0
                // Only update if the selected month end is different from the current one
                if (currentSelectedMonthEnd != selectedMonthValue) {
                    homeViewModel.updateFilters(year = null, monthStart = null, monthEnd = selectedMonthValue)
                }
            }
        }
    }

    private fun convertToPersianNumerals(input: String): String {
        val persianDigits = arrayOf("۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹")
        var result = input
        for (i in 0..9) {
            result = result.replace(i.toString(), persianDigits[i])
        }
        return result
    }

    private fun navigateToEditTransaction(shortUuid: String, mode: String, transaction: Any) {
        val bundle = Bundle().apply {
            putString("shortUuid", shortUuid)
            putString("mode", mode)
            putParcelable("transaction", transaction as android.os.Parcelable)
        }
        findNavController().navigate(R.id.action_nav_home_to_editTransactionFragment, bundle)
    }

    private fun navigateToEditEvent(shortUuid: String, event: ir.rahavardit.ariel.data.model.EventObject) {
        val bundle = Bundle().apply {
            putString("shortUuid", shortUuid)
            putParcelable("event", event)
        }
        findNavController().navigate(R.id.action_nav_home_to_editEventFragment, bundle)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
