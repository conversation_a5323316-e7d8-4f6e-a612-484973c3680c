<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="6dp"
    tools:context=".ui.edittransaction.EditTransactionFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingBottom="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Edit Transaction Form Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_edit_transaction"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="4dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp"
                app:strokeColor="@color/card_border"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/tv_edit_transaction_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/edit"
                        android:textAppearance="?attr/textAppearanceSubtitle1"
                        android:textColor="?attr/colorOnSurface"
                        android:textStyle="bold" />

                    <!-- Date Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_transaction_date"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/transaction_date">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_transaction_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clickable="true"
                            android:focusable="false"
                            android:inputType="none" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Title Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_transaction_title"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/transaction_title">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_transaction_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Amount Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_transaction_amount"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/transaction_amount">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_transaction_amount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Mode Dropdown -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_transaction_mode"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/transaction_mode">

                        <AutoCompleteTextView
                            android:id="@+id/dropdown_transaction_mode"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Bank Dropdown -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_transaction_bank"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/transaction_bank">

                        <AutoCompleteTextView
                            android:id="@+id/dropdown_transaction_bank"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Category Dropdown -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_transaction_category"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/transaction_category">

                        <AutoCompleteTextView
                            android:id="@+id/dropdown_transaction_category"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Tags Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_transaction_tags"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/transaction_tags">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_transaction_tags"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clickable="true"
                            android:focusable="false"
                            android:inputType="none" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Submit Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_submit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:text="@string/update"
                        app:cornerRadius="8dp" />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="4dp"
        android:gravity="center"
        android:textAppearance="?attr/textAppearanceBody1"
        android:textColor="?attr/colorError"
        android:visibility="gone"
        tools:text="Error message"
        tools:visibility="visible" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
